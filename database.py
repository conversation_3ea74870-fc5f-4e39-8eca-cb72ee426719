from models import User, Task
from extensions import db
from datetime import datetime

def create_user(name: str, phone: str) -> User:
    """Создание нового пользователя"""
    is_admin = name.endswith('_admin')
    if is_admin:
        name = name[:-6]  # Убираем суффикс _admin
    
    user = User(name=name, phone=phone, is_admin=is_admin)
    db.session.add(user)
    db.session.commit()
    return user

def get_user_by_phone(phone: str) -> User:
    """Получение пользователя по номеру телефона"""
    return User.query.filter_by(phone=phone).first()

def create_task(title, description, creator_id, assignee_id, deadline, task_type):
    """Создание новой задачи"""
    new_task = Task(
        title=title,
        description=description,
        creator_id=creator_id,
        assignee_id=assignee_id,
        deadline=deadline,
        task_type=task_type,
        status="ждет принятия задачи"
    )
    
    db.session.add(new_task)
    db.session.commit()
    return new_task

def get_user_tasks(user_id: int, page: int = 1, per_page: int = 15):
    """Получение задач пользователя с пагинацией"""
    return Task.query.filter_by(assignee_id=user_id).paginate(
        page=page, per_page=per_page, error_out=False
    )

def get_all_tasks(page: int = 1, per_page: int = 15):
    """Получение всех задач с пагинацией"""
    return Task.query.paginate(
        page=page, per_page=per_page, error_out=False
    )

def update_task_status(task_id: int, new_status: str) -> bool:
    """Обновление статуса задачи"""
    task = Task.query.get(task_id)
    if task:
        task.status = new_status
        db.session.commit()
        return True
    return False

def delete_task(task_id: int) -> bool:
    """Удаление задачи"""
    task = Task.query.get(task_id)
    if task:
        db.session.delete(task)
        db.session.commit()
        return True
    return False 