{% extends "base.html" %}

{% block title %}Создание задачи{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h3 class="text-center">Создание новой задачи</h3>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="mb-3">
                        <label for="title" class="form-label">Название задачи</label>
                        <input type="text" class="form-control" id="title" name="title" required>
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">Описание задачи</label>
                        <textarea class="form-control" id="description" name="description" rows="3" required></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="assignee" class="form-label">Исполнитель</label>
                        <select class="form-select" id="assignee" name="assignee_id" required>
                            <option value="">Выберите исполнителя</option>
                            {% for employee in employees %}
                                <option value="{{ employee.id }}">{{ employee.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="deadline" class="form-label">Срок выполнения</label>
                        <input type="date" class="form-control" id="deadline" name="deadline" required>
                    </div>
                    <div class="mb-3">
                        <label for="task_type" class="form-label">Тип задачи</label>
                        <select class="form-select" id="task_type" name="task_type" required>
                            <option value="личная">Личная</option>
                            <option value="общая">Общая</option>
                        </select>
                    </div>
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">Создать задачу</button>
                        <a href="{{ url_for('index') }}" class="btn btn-secondary">Отмена</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %} 