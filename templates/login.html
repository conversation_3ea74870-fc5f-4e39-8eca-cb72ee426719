{% extends "base.html" %}

{% block title %}🔑 Вход - TaskMaster Pro{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-5 col-md-7 col-sm-9">
        <div class="text-center mb-4">
            <div class="display-1 mb-3">🚀</div>
            <h2 class="text-gradient">Добро пожаловать в TaskMaster Pro</h2>
            <p class="text-secondary">Войдите в свой аккаунт для продолжения</p>
        </div>

        <div class="card">
            <div class="card-header text-center">
                <h3 class="mb-0">🔑 Вход в систему</h3>
            </div>
            <div class="card-body p-4">
                <form method="POST" action="{{ url_for('login') }}" class="needs-validation" novalidate>
                    <div class="mb-4">
                        <label for="phone" class="form-label">
                            📱 Номер телефона
                        </label>
                        <div class="input-group">
                            <span class="input-group-text" style="background: var(--bg-tertiary); border: var(--border-neon); color: var(--text-secondary);">
                                📞
                            </span>
                            <input type="tel"
                                   class="form-control"
                                   id="phone"
                                   name="phone"
                                   required
                                   placeholder="Введите номер телефона"
                                   pattern="[0-9+\-\s\(\)]+"
                                   title="Введите корректный номер телефона">
                            <div class="invalid-feedback">
                                Пожалуйста, введите корректный номер телефона
                            </div>
                        </div>
                        <small class="form-text text-muted">
                            Например: +7 (999) 123-45-67
                        </small>
                    </div>

                    <div class="d-grid gap-3">
                        <button type="submit" class="btn btn-primary btn-lg">
                            🚀 Войти в систему
                        </button>
                    </div>
                </form>
            </div>
            <div class="card-footer text-center">
                <p class="mb-0 text-secondary">
                    Нет аккаунта?
                    <a href="{{ url_for('register') }}" class="text-neon text-decoration-none">
                        ✨ Зарегистрироваться
                    </a>
                </p>
            </div>
        </div>

        <!-- Features Section -->
        <div class="row mt-5 text-center">
            <div class="col-4">
                <div class="text-neon display-6">⚡</div>
                <small class="text-muted">Быстро</small>
            </div>
            <div class="col-4">
                <div class="text-neon display-6">🔒</div>
                <small class="text-muted">Безопасно</small>
            </div>
            <div class="col-4">
                <div class="text-neon display-6">🎯</div>
                <small class="text-muted">Эффективно</small>
            </div>
        </div>
    </div>
</div>

<style>
.input-group-text {
    background: var(--bg-tertiary);
    border: var(--border-neon);
    color: var(--text-secondary);
}

.form-control:focus + .input-group-text {
    border-color: var(--neon-blue);
}

.invalid-feedback {
    color: var(--neon-pink);
}

.form-control:invalid {
    border-color: var(--neon-pink);
    box-shadow: 0 0 10px rgba(233, 69, 96, 0.3);
}

.form-control:valid {
    border-color: #00ff88;
    box-shadow: 0 0 10px rgba(0, 255, 136, 0.3);
}
</style>
{% endblock %}