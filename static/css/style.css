/* ===== DARK MODERN THEME ===== */

/* CSS Variables for Dark Theme */
:root {
  --bg-primary: #0f0f23;
  --bg-secondary: #1a1a2e;
  --bg-tertiary: #16213e;
  --neon-blue: #00d4ff;
  --neon-pink: #e94560;
  --neon-purple: #9d4edd;
  --text-primary: #ffffff;
  --text-secondary: #b8b8b8;
  --text-muted: #6c757d;
  --shadow-neon: 0 0 20px rgba(0, 212, 255, 0.3);
  --shadow-dark: 0 10px 30px rgba(0, 0, 0, 0.5);
  --gradient-primary: linear-gradient(135deg, #16213e 0%, #0f3460 100%);
  --gradient-neon: linear-gradient(45deg, #00d4ff, #e94560);
  --border-neon: 1px solid rgba(0, 212, 255, 0.3);
}

/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  background: var(--bg-primary);
  color: var(--text-primary);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
  line-height: 1.6;
  overflow-x: hidden;
  position: relative;
}

/* Animated Background */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(233, 69, 96, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(157, 78, 221, 0.05) 0%, transparent 50%);
  z-index: -1;
  animation: backgroundPulse 8s ease-in-out infinite alternate;
}

@keyframes backgroundPulse {
  0% { opacity: 0.3; }
  100% { opacity: 0.7; }
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  margin-bottom: 1rem;
  background: var(--gradient-neon);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Animations */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes neonGlow {
  0%, 100% {
    box-shadow: 0 0 5px var(--neon-blue), 0 0 10px var(--neon-blue), 0 0 15px var(--neon-blue);
  }
  50% {
    box-shadow: 0 0 10px var(--neon-blue), 0 0 20px var(--neon-blue), 0 0 30px var(--neon-blue);
  }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

/* Container */
.container {
  animation: fadeIn 0.8s ease-out;
}

/* Navigation */
.navbar {
  background: rgba(26, 26, 46, 0.95) !important;
  backdrop-filter: blur(10px);
  border-bottom: var(--border-neon);
  box-shadow: var(--shadow-dark);
  transition: all 0.3s ease;
}

.navbar-brand {
  font-weight: 800;
  font-size: 1.5rem;
  background: var(--gradient-neon);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-decoration: none !important;
}

.nav-link {
  color: var(--text-secondary) !important;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
  padding: 0.5rem 1rem !important;
}

.nav-link::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: var(--gradient-neon);
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.nav-link:hover {
  color: var(--neon-blue) !important;
  text-shadow: 0 0 10px var(--neon-blue);
}

.nav-link:hover::before {
  width: 80%;
}

/* Cards */
.card {
  background: var(--bg-secondary);
  border: var(--border-neon);
  border-radius: 15px;
  box-shadow: var(--shadow-dark);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  animation: slideUp 0.6s ease-out;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.1), transparent);
  transition: left 0.8s ease;
}

.card:hover::before {
  left: 100%;
}

.card:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow: var(--shadow-neon), var(--shadow-dark);
  border-color: var(--neon-blue);
}

.card-header {
  background: var(--gradient-primary);
  border-bottom: var(--border-neon);
  border-radius: 15px 15px 0 0 !important;
  padding: 1.5rem;
}

.card-body {
  padding: 1.5rem;
  background: var(--bg-secondary);
}

.card-footer {
  background: var(--bg-tertiary);
  border-top: var(--border-neon);
  padding: 1rem 1.5rem;
  border-radius: 0 0 15px 15px;
}

.card-title {
  color: var(--text-primary);
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.card-text {
  color: var(--text-secondary);
  line-height: 1.6;
}

/* Buttons */
.btn {
  border-radius: 25px;
  font-weight: 600;
  padding: 0.75rem 2rem;
  border: none;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-size: 0.9rem;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: var(--gradient-neon);
  color: var(--text-primary);
  box-shadow: 0 5px 15px rgba(0, 212, 255, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 212, 255, 0.5);
  color: var(--text-primary);
}

.btn-secondary {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  border: var(--border-neon);
}

.btn-secondary:hover {
  background: var(--bg-secondary);
  color: var(--neon-blue);
  border-color: var(--neon-blue);
  transform: translateY(-2px);
}

.btn-success {
  background: linear-gradient(45deg, #00ff88, #00cc6a);
  color: var(--text-primary);
  box-shadow: 0 5px 15px rgba(0, 255, 136, 0.3);
}

.btn-success:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 255, 136, 0.5);
  color: var(--text-primary);
}

.btn-warning {
  background: linear-gradient(45deg, #ffb347, #ff8c00);
  color: var(--text-primary);
  box-shadow: 0 5px 15px rgba(255, 179, 71, 0.3);
}

.btn-warning:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(255, 179, 71, 0.5);
  color: var(--text-primary);
}

.btn-danger {
  background: linear-gradient(45deg, var(--neon-pink), #dc3545);
  color: var(--text-primary);
  box-shadow: 0 5px 15px rgba(233, 69, 96, 0.3);
}

.btn-danger:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(233, 69, 96, 0.5);
  color: var(--text-primary);
}

.btn-sm {
  padding: 0.5rem 1.5rem;
  font-size: 0.8rem;
}

/* Forms */
.form-control, .form-select {
  background: var(--bg-tertiary);
  border: var(--border-neon);
  border-radius: 10px;
  color: var(--text-primary);
  padding: 0.75rem 1rem;
  transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
  background: var(--bg-secondary);
  border-color: var(--neon-blue);
  box-shadow: 0 0 0 0.2rem rgba(0, 212, 255, 0.25);
  color: var(--text-primary);
}

.form-control::placeholder {
  color: var(--text-muted);
}

.form-label {
  color: var(--text-secondary);
  font-weight: 600;
  margin-bottom: 0.5rem;
}

/* Badges */
.badge {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 0.75rem;
  position: relative;
  overflow: hidden;
}

.bg-warning {
  background: linear-gradient(45deg, #ffb347, #ff8c00) !important;
  color: var(--text-primary) !important;
  animation: pulse 2s infinite;
}

.bg-primary {
  background: var(--gradient-neon) !important;
  color: var(--text-primary) !important;
  animation: neonGlow 2s infinite;
}

.bg-success {
  background: linear-gradient(45deg, #00ff88, #00cc6a) !important;
  color: var(--text-primary) !important;
}

/* Alerts */
.alert {
  background: var(--bg-secondary);
  border: var(--border-neon);
  border-radius: 10px;
  color: var(--text-primary);
  border-left: 4px solid var(--neon-blue);
  animation: slideUp 0.5s ease-out;
}

.alert-info {
  border-left-color: var(--neon-blue);
  background: rgba(0, 212, 255, 0.1);
}

.alert-success {
  border-left-color: #00ff88;
  background: rgba(0, 255, 136, 0.1);
}

.alert-warning {
  border-left-color: #ffb347;
  background: rgba(255, 179, 71, 0.1);
}

.alert-danger {
  border-left-color: var(--neon-pink);
  background: rgba(233, 69, 96, 0.1);
}

/* Modal */
.modal-content {
  background: var(--bg-secondary);
  border: var(--border-neon);
  border-radius: 15px;
  box-shadow: var(--shadow-dark);
}

.modal-header {
  background: var(--gradient-primary);
  border-bottom: var(--border-neon);
  border-radius: 15px 15px 0 0;
}

.modal-title {
  color: var(--text-primary);
  font-weight: 700;
}

.modal-body {
  color: var(--text-secondary);
}

.modal-footer {
  background: var(--bg-tertiary);
  border-top: var(--border-neon);
  border-radius: 0 0 15px 15px;
}

.btn-close {
  filter: invert(1);
  opacity: 0.7;
}

.btn-close:hover {
  opacity: 1;
}

/* Pagination */
.pagination {
  margin-top: 2rem;
}

.page-link {
  background: var(--bg-tertiary);
  border: var(--border-neon);
  color: var(--text-secondary);
  padding: 0.75rem 1rem;
  margin: 0 0.25rem;
  border-radius: 10px;
  transition: all 0.3s ease;
}

.page-link:hover {
  background: var(--bg-secondary);
  color: var(--neon-blue);
  border-color: var(--neon-blue);
  transform: translateY(-2px);
}

.page-item.active .page-link {
  background: var(--gradient-neon);
  border-color: var(--neon-blue);
  color: var(--text-primary);
  box-shadow: var(--shadow-neon);
}

/* Special Effects */
.border-danger {
  border-color: var(--neon-pink) !important;
  box-shadow: 0 0 15px rgba(233, 69, 96, 0.5) !important;
  animation: pulse 1.5s infinite;
}

/* Task Status Indicators */
.task-overdue {
  position: relative;
}

.task-overdue::after {
  content: '⚠️';
  position: absolute;
  top: -5px;
  right: -5px;
  font-size: 1.2rem;
  animation: pulse 1s infinite;
}

/* Stagger Animation for Cards */
.card:nth-child(1) { animation-delay: 0.1s; }
.card:nth-child(2) { animation-delay: 0.2s; }
.card:nth-child(3) { animation-delay: 0.3s; }
.card:nth-child(4) { animation-delay: 0.4s; }
.card:nth-child(5) { animation-delay: 0.5s; }

/* Loading Animation */
@keyframes shimmer {
  0% { background-position: -200px 0; }
  100% { background-position: calc(200px + 100%) 0; }
}

.loading {
  background: linear-gradient(90deg, var(--bg-secondary) 25%, var(--bg-tertiary) 50%, var(--bg-secondary) 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Hover Effects for Interactive Elements */
.btn-group .btn {
  margin-right: 0.5rem;
}

.btn-group .btn:last-child {
  margin-right: 0;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-primary);
}

::-webkit-scrollbar-thumb {
  background: var(--gradient-neon);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--neon-blue);
}

/* Text Selection */
::selection {
  background: rgba(0, 212, 255, 0.3);
  color: var(--text-primary);
}

/* Focus Styles */
*:focus {
  outline: 2px solid var(--neon-blue);
  outline-offset: 2px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .card {
    margin-bottom: 1rem;
  }

  .btn {
    padding: 0.5rem 1.5rem;
    font-size: 0.8rem;
  }

  .navbar-brand {
    font-size: 1.2rem;
  }

  h1 {
    font-size: 1.8rem;
  }

  h2 {
    font-size: 1.5rem;
  }
}

/* Utility Classes */
.text-neon {
  color: var(--neon-blue);
  text-shadow: 0 0 10px var(--neon-blue);
}

.text-gradient {
  background: var(--gradient-neon);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.glow {
  box-shadow: var(--shadow-neon);
}

.pulse-animation {
  animation: pulse 2s infinite;
}

/* Form Enhancements */
.form-floating .form-control:focus ~ label,
.form-floating .form-control:not(:placeholder-shown) ~ label {
  color: var(--neon-blue);
}

/* Button Group Enhancements */
.btn-group-vertical .btn {
  margin-bottom: 0.5rem;
}

.btn-group-vertical .btn:last-child {
  margin-bottom: 0;
}