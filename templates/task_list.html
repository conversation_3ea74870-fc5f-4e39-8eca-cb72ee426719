{% macro render_task_list(tasks, show_assignee=False, now=None, is_admin=False, current_user_id=None) %}
    {% for task in tasks.items %}
        <div class="card mb-4 {% if now and task.deadline < now %}border-danger task-overdue{% endif %}">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        {% if task.task_type == 'личная' %}
                            <span class="display-6">👤</span>
                        {% else %}
                            <span class="display-6">👥</span>
                        {% endif %}
                    </div>
                    <div>
                        <h5 class="card-title mb-0 text-gradient">{{ task.title }}</h5>
                        <small class="text-muted">
                            {% if now and task.deadline < now %}
                                ⚠️ Просрочена
                            {% else %}
                                📅 {{ task.deadline.strftime('%d.%m.%Y %H:%M') }}
                            {% endif %}
                        </small>
                    </div>
                </div>
                <div class="d-flex align-items-center">
                    {% if task.status == 'ждет принятия задачи' %}
                        <span class="badge bg-warning pulse-animation me-2">
                            ⏳ {{ task.status }}
                        </span>
                    {% elif task.status == 'в процессе' %}
                        <span class="badge bg-primary me-2">
                            🔄 {{ task.status }}
                        </span>
                    {% else %}
                        <span class="badge bg-success me-2">
                            ✅ {{ task.status }}
                        </span>
                    {% endif %}

                    {% if task.task_type == 'личная' %}
                        <span class="badge" style="background: var(--gradient-neon);">
                            ✨ Личная
                        </span>
                    {% else %}
                        <span class="badge" style="background: var(--bg-tertiary); color: var(--text-secondary);">
                            🌐 Общая
                        </span>
                    {% endif %}
                </div>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <p class="card-text">{{ task.description }}</p>
                </div>

                <div class="row g-3">
                    <div class="col-md-6">
                        <div class="d-flex flex-column gap-2">
                            {% if show_assignee %}
                                <div class="d-flex align-items-center">
                                    <span class="me-2">👤</span>
                                    <small class="text-muted">Исполнитель:</small>
                                    <strong class="ms-1 text-neon">{{ task.assignee.name }}</strong>
                                </div>
                            {% endif %}
                            <div class="d-flex align-items-center">
                                <span class="me-2">👨‍💼</span>
                                <small class="text-muted">Создал:</small>
                                <strong class="ms-1">{{ task.creator.name }}</strong>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-flex flex-column gap-2">
                            <div class="d-flex align-items-center">
                                <span class="me-2">⏰</span>
                                <small class="text-muted">Срок:</small>
                                <strong class="ms-1 {% if now and task.deadline < now %}text-danger{% else %}text-warning{% endif %}">
                                    {{ task.deadline.strftime('%d.%m.%Y %H:%M') }}
                                </strong>
                            </div>
                            <div class="d-flex align-items-center">
                                <span class="me-2">📅</span>
                                <small class="text-muted">Создано:</small>
                                <strong class="ms-1">{{ task.created_at.strftime('%d.%m.%Y %H:%M') }}</strong>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Progress Bar for Task Status -->
                <div class="mt-3">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <small class="text-muted">Прогресс выполнения</small>
                        <small class="text-muted">
                            {% if task.status == 'ждет принятия задачи' %}0%
                            {% elif task.status == 'в процессе' %}50%
                            {% else %}100%{% endif %}
                        </small>
                    </div>
                    <div class="progress" style="height: 8px; background: var(--bg-tertiary);">
                        <div class="progress-bar"
                             style="
                                {% if task.status == 'ждет принятия задачи' %}
                                    width: 0%; background: var(--gradient-neon);
                                {% elif task.status == 'в процессе' %}
                                    width: 50%; background: var(--gradient-neon); animation: pulse 2s infinite;
                                {% else %}
                                    width: 100%; background: linear-gradient(45deg, #00ff88, #00cc6a);
                                {% endif %}
                             "
                             role="progressbar">
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <div class="d-flex flex-wrap gap-2 justify-content-between align-items-center">
                    <div class="d-flex flex-wrap gap-2">
                        {% if task.status == 'ждет принятия задачи' and task.assignee_id == current_user_id %}
                            <form method="POST" action="{{ url_for('update_task_status_route', task_id=task.id) }}" class="d-inline">
                                <input type="hidden" name="status" value="в процессе">
                                <button type="submit" class="btn btn-success btn-sm">
                                    ✅ Принять задачу
                                </button>
                            </form>
                        {% endif %}

                        {% if task.status == 'в процессе' and task.assignee_id == current_user_id %}
                            <form method="POST" action="{{ url_for('update_task_status_route', task_id=task.id) }}" class="d-inline">
                                <input type="hidden" name="status" value="выполнена">
                                <button type="submit" class="btn btn-primary btn-sm">
                                    🎯 Завершить задачу
                                </button>
                            </form>
                        {% endif %}

                        {% if is_admin %}
                            <a href="{{ url_for('edit_task', task_id=task.id) }}" class="btn btn-warning btn-sm">
                                ✏️ Редактировать
                            </a>
                            <button type="button" class="btn btn-danger btn-sm" data-bs-toggle="modal" data-bs-target="#deleteModal{{ task.id }}">
                                🗑️ Удалить
                            </button>
                        {% endif %}
                    </div>

                    <!-- Task Priority Indicator -->
                    <div class="d-flex align-items-center">
                        {% if now and task.deadline < now %}
                            <span class="badge" style="background: var(--neon-pink); animation: pulse 1s infinite;">
                                🚨 Просрочена
                            </span>
                        {% elif (task.deadline - now).days <= 1 %}
                            <span class="badge" style="background: linear-gradient(45deg, #ffb347, #ff8c00);">
                                ⚡ Срочно
                            </span>
                        {% else %}
                            <span class="badge" style="background: var(--bg-tertiary); color: var(--text-secondary);">
                                📅 В срок
                            </span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        {% if is_admin %}
            <!-- Модальное окно подтверждения удаления -->
            <div class="modal fade" id="deleteModal{{ task.id }}" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Подтверждение удаления</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <p>Вы уверены, что хотите удалить задачу "{{ task.title }}"?</p>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Отмена</button>
                            <form method="POST" action="{{ url_for('delete_task_route', task_id=task.id) }}" class="d-inline">
                                <button type="submit" class="btn btn-danger">Удалить</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        {% endif %}
    {% else %}
        <div class="alert alert-info">Задачи не найдены</div>
    {% endfor %}

    {% if tasks.pages > 1 %}
        <nav aria-label="Page navigation">
            <ul class="pagination justify-content-center">
                {% for page in range(1, tasks.pages + 1) %}
                    <li class="page-item {% if page == tasks.page %}active{% endif %}">
                        <a class="page-link" href="{{ url_for(request.endpoint, page=page) }}">{{ page }}</a>
                    </li>
                {% endfor %}
            </ul>
        </nav>
    {% endif %}
{% endmacro %}